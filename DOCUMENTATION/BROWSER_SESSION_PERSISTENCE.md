# Browser Session Persistence for E2B Sandbox

This document explains how to use the browser session persistence functionality in the E2B Browser Use module.

## Overview

Browser session persistence allows you to maintain browser state (cookies, local storage, session data, preferences) across different E2B sandbox instances. This is particularly useful for:

- Maintaining login sessions across automation runs
- Preserving user preferences and settings
- Avoiding repeated authentication steps
- Continuing workflows from where they left off

## How It Works

The system automatically:

1. **On Sandbox Startup**: Restores browser session data from `/home/<USER>/10x-sales-agent/cookie/cookie.json` to `~/cookie.json` in the sandbox
2. **During Execution**: Browser automation tasks use the restored session data via `storage_state='~/cookie.json'`
3. **On Sandbox Cleanup**: Downloads current browser session data from the sandbox back to `/home/<USER>/10x-sales-agent/cookie/cookie.json` for the next run

## File Structure

### Local Storage (Server)
```
/home/<USER>/10x-sales-agent/cookie/
└── cookie.json              # Browser session state (cookies, localStorage, sessionStorage, etc.)
```

### Sandbox Storage
```
~/cookie.json                # Browser session state file used by storage_state parameter
```

## Usage Examples

### Automatic Session Persistence (Default)

```python
import e2b_browser_use

# Browser session is automatically restored and saved
result = e2b_browser_use.main(query="Login to website and perform tasks")
```

### Disable Automatic Session Restoration

```python
import e2b_browser_use

# Start with fresh browser session
result = e2b_browser_use.main(
    query="Perform tasks with clean session",
    restore_browser_session=False
)
```

### Manual Session Management

```python
import e2b_browser_use

tasks = [
    # Manually restore session at specific point
    {'type': 'restore_browser_session', 'local_session_dir': '/home/<USER>/cookie'},
    
    # Perform browser automation
    {'type': 'bedrock', 'query': 'Navigate to dashboard'},
    
    # Manually save session at specific point
    {'type': 'save_browser_session', 'local_session_dir': '/home/<USER>/cookie'},
    
    # Continue with more tasks
    {'type': 'bedrock', 'query': 'Download reports'}
]

result = e2b_browser_use.main(tasks=tasks)
```

### Custom Cookie File Location

```python
import e2b_browser_use

tasks = [
    # Use custom file for session storage
    {'type': 'restore_browser_session', 'local_cookie_file': '/home/<USER>/10x-sales-agent/custom_cookie.json'},
    {'type': 'bedrock', 'query': 'Perform automation'},
    {'type': 'save_browser_session', 'local_cookie_file': '/home/<USER>/10x-sales-agent/custom_cookie.json'}
]

result = e2b_browser_use.main(tasks=tasks)
```

## API Reference

### Functions

#### `download_cookie_json(desktop, local_cookie_file="/home/<USER>/10x-sales-agent/cookie/cookie.json")`

Downloads browser session data from sandbox to local file.

**Parameters:**
- `desktop`: E2B desktop sandbox instance
- `local_cookie_file`: Local file path to save cookie.json (default: "/home/<USER>/10x-sales-agent/cookie/cookie.json")

**Returns:**
- `bool`: True if successful, False otherwise

#### `upload_cookie_json(desktop, local_cookie_file="/home/<USER>/10x-sales-agent/cookie/cookie.json")`

Uploads browser session data from local file to sandbox.

**Parameters:**
- `desktop`: E2B desktop sandbox instance
- `local_cookie_file`: Local file path containing cookie.json (default: "/home/<USER>/10x-sales-agent/cookie/cookie.json")

**Returns:**
- `bool`: True if successful, False otherwise

### Task Types

#### `save_browser_session`

Manually save browser session data during task execution.

```python
{'type': 'save_browser_session', 'local_cookie_file': '/path/to/cookie.json'}
```

#### `restore_browser_session`

Manually restore browser session data during task execution.

```python
{'type': 'restore_browser_session', 'local_cookie_file': '/path/to/cookie.json'}
```

## File Handling

The system handles the cookie.json file as a JSON text file:

- **cookie.json**: Contains browser session state in JSON format including cookies, localStorage, sessionStorage, and other browser state data
- **Transfer**: Simple text file transfer between local and remote locations

## Error Handling

The system includes comprehensive error handling:

- Missing directories are created automatically
- File read/write errors are logged but don't stop execution
- Failed transfers are reported with detailed error messages
- Partial failures allow successful files to be processed

## Best Practices

1. **Regular Backups**: The `/home/<USER>/10x-sales-agent/cookie/cookie.json` file contains important session data - consider backing it up
2. **Clean Sessions**: Occasionally start with `restore_browser_session=False` to ensure clean state
3. **Custom Files**: Use different cookie.json files for different automation workflows
4. **Monitor Logs**: Check logs for session persistence success/failure messages with `[COOKIE_JSON]` prefix

## Troubleshooting

### Session Not Restored
- Check if `/home/<USER>/10x-sales-agent/cookie/cookie.json` file exists
- Verify file permissions on the cookie.json file
- Look for error messages in logs with `[COOKIE_JSON]` prefix

### Session Not Saved
- Ensure browser automation tasks actually created session data
- Check sandbox logs for file system errors
- Verify the browser can write to `~/cookie.json` in sandbox

### Performance Issues
- Cookie.json file transfer is much faster than directory-based approach
- File size is typically small (few KB to few MB)
- Monitor disk space usage for the cookie directory

## Security Considerations

- Session data may contain sensitive information (cookies, tokens)
- Ensure proper file permissions on session directories
- Consider encrypting session data for sensitive applications
- Regularly rotate session data for security

## Logging

All browser session operations are logged with the `[BROWSER_SESSION]` prefix:

```
[BROWSER_SESSION] Starting download of browser session data to /home/<USER>/cookie
[BROWSER_SESSION] Found 45 files to download
[BROWSER_SESSION] Successfully downloaded: 45 files
[BROWSER_SESSION] Browser session download completed
```

Monitor these logs to track session persistence operations and troubleshoot issues.
