#!/usr/bin/env python3
"""
Token Usage Analysis Script

This script analyzes log files to extract and summarize LLM token usage information.
It identifies task boundaries and groups LLM calls by tasks.
"""

import re
import os
from datetime import datetime
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass


@dataclass
class LLMCall:
    """Represents a single LLM call with token information."""
    timestamp: str
    agent_id: str
    session_id: str
    messages: int
    tokens: int
    characters: int
    has_image: bool
    tools_count: int
    step_number: Optional[int] = None
    line_number: int = 0


@dataclass
class TaskSummary:
    """Represents a task with its associated LLM calls."""
    task_id: str
    task_description: str
    start_time: str
    end_time: str
    total_tokens: int
    total_calls: int
    llm_calls: List[LLMCall]
    success: bool = False


class TokenUsageAnalyzer:
    """Analyzes log files for LLM token usage patterns."""
    
    def __init__(self):
        self.llm_call_pattern = re.compile(
            r'LLM call => ChatBedrockConverse \[✉️ (\d+) msg, ~(\d+) tk, (\d+) char, 📷 (img)?\] => JSO<PERSON> out \+ 🔨 (\d+) tools'
        )
        self.agent_pattern = re.compile(r'Agent🅰 ([a-f0-9]+) on 🆂 ([a-f0-9]+\.\d+)')
        self.step_pattern = re.compile(r'📍 Step (\d+):')
        self.task_start_patterns = [
            re.compile(r'Starting.*task'),
            re.compile(r'Task.*started'),
            re.compile(r'Beginning.*execution'),
            re.compile(r'run_bedrock_task.*started'),
        ]
        self.task_end_patterns = [
            re.compile(r'Task completed.*successfully'),
            re.compile(r'Task completed.*without success'),
            re.compile(r'✅ Task completed'),
            re.compile(r'❌ Task completed'),
            re.compile(r'run_bedrock_task.*completed'),
        ]
    
    def extract_timestamp(self, line: str) -> str:
        """Extract timestamp from log line."""
        # Pattern: Jul 22 18:34:42 or 2025-07-22 18:34:42
        timestamp_match = re.search(r'(Jul \d+ \d+:\d+:\d+|\d{4}-\d{2}-\d{2} \d+:\d+:\d+)', line)
        return timestamp_match.group(1) if timestamp_match else ""
    
    def parse_llm_call(self, line: str, line_number: int) -> Optional[LLMCall]:
        """Parse a single LLM call from a log line."""
        llm_match = self.llm_call_pattern.search(line)
        if not llm_match:
            return None
        
        agent_match = self.agent_pattern.search(line)
        step_match = self.step_pattern.search(line)
        
        timestamp = self.extract_timestamp(line)
        agent_id = agent_match.group(1) if agent_match else "unknown"
        session_id = agent_match.group(2) if agent_match else "unknown"
        
        return LLMCall(
            timestamp=timestamp,
            agent_id=agent_id,
            session_id=session_id,
            messages=int(llm_match.group(1)),
            tokens=int(llm_match.group(2)),
            characters=int(llm_match.group(3)),
            has_image=bool(llm_match.group(4)),
            tools_count=int(llm_match.group(5)),
            step_number=int(step_match.group(1)) if step_match else None,
            line_number=line_number
        )
    
    def identify_task_boundaries(self, lines: List[str]) -> List[Tuple[int, int, str, bool]]:
        """Identify task start and end boundaries in the log."""
        boundaries = []

        # Look for clear task completion markers
        task_completion_pattern = re.compile(r'(✅ Task completed successfully|❌ Task completed without success|Task completed.*successfully|Task completed.*without success)')

        # Find all task completion lines
        completion_lines = []
        for i, line in enumerate(lines):
            if task_completion_pattern.search(line):
                success = "successfully" in line or "✅" in line
                completion_lines.append((i, success))

        if not completion_lines:
            # No clear task boundaries found, return empty list
            return boundaries

        # For each completion, work backwards to find the start
        for i, (completion_line, success) in enumerate(completion_lines):
            # Look for the start of this task
            start_line = 0
            if i > 0:
                # Start after the previous task's completion
                start_line = completion_lines[i-1][0] + 1

            # Create a meaningful description based on the log content
            task_desc = self.extract_task_description(lines, start_line, completion_line)

            boundaries.append((start_line, completion_line, task_desc, success))

        return boundaries

    def extract_task_description(self, lines: List[str], start_line: int, end_line: int) -> str:
        """Extract a meaningful task description from the log lines."""
        # Look for key indicators in the task
        keywords_found = []

        for i in range(start_line, min(end_line + 1, len(lines))):
            line = lines[i]

            # Look for Russian keywords being searched
            if 'игрушки' in line:
                keywords_found.append("Russian keyword 'игрушки' (toys)")
            elif 'бумажный' in line:
                keywords_found.append("Russian keyword 'бумажный' (paper)")

            # Look for export activities
            if 'Export' in line and 'CSV' in line:
                keywords_found.append("CSV export")
            elif 'Downloaded file' in line:
                keywords_found.append("file download")

            # Look for search results
            if re.search(r'\d+ results', line):
                match = re.search(r'(\d+) results', line)
                if match:
                    keywords_found.append(f"{match.group(1)} search results")

        if keywords_found:
            return f"Browser automation task: {', '.join(set(keywords_found))}"
        else:
            return f"Browser automation task (lines {start_line+1}-{end_line+1})"
    
    def analyze_log_file(self, filepath: str) -> List[TaskSummary]:
        """Analyze a single log file and return task summaries."""
        print(f"Analyzing log file: {filepath}")

        with open(filepath, 'r', encoding='utf-8') as f:
            lines = f.readlines()

        # Extract all LLM calls
        llm_calls = []
        for i, line in enumerate(lines):
            call = self.parse_llm_call(line, i+1)
            if call:
                llm_calls.append(call)

        print(f"Found {len(llm_calls)} raw LLM calls in {filepath}")

        # Deduplicate LLM calls based on unique combinations
        unique_calls = []
        seen_calls = set()

        for call in llm_calls:
            # Create a unique key based on timestamp, tokens, step, and agent
            call_key = (call.timestamp, call.tokens, call.step_number, call.agent_id, call.session_id)
            if call_key not in seen_calls:
                seen_calls.add(call_key)
                unique_calls.append(call)

        llm_calls = unique_calls
        print(f"After deduplication: {len(llm_calls)} unique LLM calls in {filepath}")

        if not llm_calls:
            return []

        # Group LLM calls by session/agent
        sessions = {}
        for call in llm_calls:
            session_key = f"{call.agent_id}_{call.session_id}"
            if session_key not in sessions:
                sessions[session_key] = []
            sessions[session_key].append(call)

        print(f"Found {len(sessions)} unique sessions in {filepath}")

        # Identify task boundaries for context
        task_boundaries = self.identify_task_boundaries(lines)
        print(f"Found {len(task_boundaries)} task completion markers in {filepath}")

        # Create task summaries
        tasks = []

        if len(sessions) == 1 and task_boundaries:
            # Single session with clear task boundaries - split by task completion
            session_calls = list(sessions.values())[0]

            for i, (start_line, end_line, desc, success) in enumerate(task_boundaries):
                # Find LLM calls within this task boundary
                task_calls = [call for call in session_calls
                             if start_line <= call.line_number <= end_line]

                if task_calls:
                    task_id = f"{os.path.basename(filepath)}_task_{i+1}"
                    total_tokens = sum(call.tokens for call in task_calls)

                    tasks.append(TaskSummary(
                        task_id=task_id,
                        task_description=desc,
                        start_time=task_calls[0].timestamp,
                        end_time=task_calls[-1].timestamp,
                        total_tokens=total_tokens,
                        total_calls=len(task_calls),
                        llm_calls=task_calls,
                        success=success
                    ))
        else:
            # Multiple sessions or no clear boundaries - group by session
            for i, (session_key, session_calls) in enumerate(sessions.items()):
                agent_id, session_id = session_key.split('_', 1)

                # Try to determine if this session was successful
                success = self.determine_session_success(lines, session_calls)

                # Create description based on session content
                desc = self.create_session_description(lines, session_calls, os.path.basename(filepath))

                task_id = f"{os.path.basename(filepath)}_session_{i+1}_{agent_id}"
                total_tokens = sum(call.tokens for call in session_calls)

                tasks.append(TaskSummary(
                    task_id=task_id,
                    task_description=desc,
                    start_time=session_calls[0].timestamp,
                    end_time=session_calls[-1].timestamp,
                    total_tokens=total_tokens,
                    total_calls=len(session_calls),
                    llm_calls=session_calls,
                    success=success
                ))

        return tasks

    def determine_session_success(self, lines: List[str], session_calls: List[LLMCall]) -> bool:
        """Determine if a session was successful based on log content."""
        start_line = session_calls[0].line_number
        end_line = session_calls[-1].line_number

        for i in range(start_line, min(end_line + 50, len(lines))):  # Look a bit beyond the last call
            line = lines[i]
            if "✅ Task completed successfully" in line or "Task completed successfully" in line:
                return True
            elif "❌ Task completed without success" in line or "without success" in line:
                return False

        # Default to True if no clear indication
        return True

    def create_session_description(self, lines: List[str], session_calls: List[LLMCall], filename: str) -> str:
        """Create a description for a session based on its content."""
        start_line = max(0, session_calls[0].line_number - 10)
        end_line = min(len(lines), session_calls[-1].line_number + 10)

        keywords = []

        for i in range(start_line, end_line):
            line = lines[i]

            # Look for specific activities
            if 'игрушки' in line:
                keywords.append("toys search")
            elif 'бумажный' in line:
                keywords.append("paper search")

            if 'CSV' in line and ('export' in line.lower() or 'download' in line.lower()):
                keywords.append("CSV export")

            if re.search(r'\d+ results', line):
                match = re.search(r'(\d+) results', line)
                if match:
                    keywords.append(f"{match.group(1)} results")

        if keywords:
            return f"Browser automation: {', '.join(set(keywords))} ({filename})"
        else:
            return f"Browser automation session ({filename})"
    
    def generate_report(self, all_tasks: List[TaskSummary]) -> str:
        """Generate a comprehensive token usage report."""
        report = []
        report.append("=" * 80)
        report.append("LLM TOKEN USAGE ANALYSIS REPORT")
        report.append("=" * 80)
        report.append(f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")
        
        # Overall summary
        total_tokens = sum(task.total_tokens for task in all_tasks)
        total_calls = sum(task.total_calls for task in all_tasks)
        successful_tasks = sum(1 for task in all_tasks if task.success)
        
        report.append("OVERALL SUMMARY")
        report.append("-" * 40)
        report.append(f"Total Tasks Analyzed: {len(all_tasks)}")
        report.append(f"Successful Tasks: {successful_tasks}")
        report.append(f"Failed Tasks: {len(all_tasks) - successful_tasks}")
        report.append(f"Total LLM Calls: {total_calls}")
        report.append(f"Total Tokens Used: {total_tokens:,}")
        report.append(f"Average Tokens per Call: {total_tokens/total_calls:.1f}" if total_calls > 0 else "Average Tokens per Call: 0")
        report.append(f"Average Tokens per Task: {total_tokens/len(all_tasks):.1f}" if all_tasks else "Average Tokens per Task: 0")
        report.append("")
        
        # Task-by-task breakdown
        report.append("TASK BREAKDOWN")
        report.append("-" * 40)
        
        for i, task in enumerate(all_tasks, 1):
            status = "✅ SUCCESS" if task.success else "❌ FAILED"
            report.append(f"\n{i}. {task.task_id} {status}")
            report.append(f"   Description: {task.task_description}")
            report.append(f"   Time Range: {task.start_time} → {task.end_time}")
            report.append(f"   Total Tokens: {task.total_tokens:,}")
            report.append(f"   LLM Calls: {task.total_calls}")
            report.append(f"   Avg Tokens/Call: {task.total_tokens/task.total_calls:.1f}" if task.total_calls > 0 else "   Avg Tokens/Call: 0")
            
            # Show individual calls if requested
            if len(task.llm_calls) <= 10:  # Only show details for smaller tasks
                report.append("   Individual Calls:")
                for j, call in enumerate(task.llm_calls, 1):
                    img_indicator = "📷" if call.has_image else "  "
                    step_info = f"Step {call.step_number}" if call.step_number else "No step"
                    report.append(f"     {j:2d}. {call.timestamp} | {call.tokens:,} tk | {img_indicator} | {step_info}")
            else:
                # For larger tasks, show summary statistics
                report.append("   Call Summary (large task):")
                report.append(f"     First Call: {task.llm_calls[0].timestamp} | {task.llm_calls[0].tokens:,} tk")
                report.append(f"     Last Call:  {task.llm_calls[-1].timestamp} | {task.llm_calls[-1].tokens:,} tk")
                report.append(f"     Min Tokens: {min(call.tokens for call in task.llm_calls):,} tk")
                report.append(f"     Max Tokens: {max(call.tokens for call in task.llm_calls):,} tk")

                # Show step progression
                steps = [call.step_number for call in task.llm_calls if call.step_number]
                if steps:
                    report.append(f"     Step Range: {min(steps)} → {max(steps)}")

                # Show calls with images
                img_calls = sum(1 for call in task.llm_calls if call.has_image)
                report.append(f"     Calls with Images: {img_calls}/{len(task.llm_calls)}")

        # Add cost estimation section
        report.append("\n" + "=" * 80)
        report.append("COST ESTIMATION (Approximate)")
        report.append("-" * 40)

        # Rough cost estimates for different models (per 1K tokens)
        cost_estimates = {
            "Claude-3.5-Sonnet": 0.003,  # $3 per 1M input tokens
            "GPT-4": 0.03,               # $30 per 1M input tokens
            "GPT-4o": 0.0025,            # $2.5 per 1M input tokens
        }

        for model, cost_per_1k in cost_estimates.items():
            total_cost = (total_tokens / 1000) * cost_per_1k
            report.append(f"{model:20s}: ${total_cost:.2f}")

        report.append(f"\nNote: These are rough estimates for input tokens only.")
        report.append(f"Actual costs may vary based on model, output tokens, and pricing changes.")
        
        return "\n".join(report)


def main():
    """Main function to analyze token usage from log files."""
    analyzer = TokenUsageAnalyzer()
    
    # Find log files in the workspace
    log_files = []
    workspace_dir = "/home/<USER>/10x-sales-agent"
    
    for filename in os.listdir(workspace_dir):
        if filename.endswith('.log') or filename.endswith('.txt'):
            filepath = os.path.join(workspace_dir, filename)
            if os.path.isfile(filepath):
                log_files.append(filepath)
    
    print(f"Found {len(log_files)} potential log files:")
    for f in log_files:
        print(f"  - {f}")
    
    # Analyze each log file
    all_tasks = []
    for log_file in log_files:
        try:
            tasks = analyzer.analyze_log_file(log_file)
            all_tasks.extend(tasks)
        except Exception as e:
            print(f"Error analyzing {log_file}: {e}")
    
    # Generate and save report
    if all_tasks:
        report = analyzer.generate_report(all_tasks)
        
        output_file = os.path.join(workspace_dir, "token-logs.txt")
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(f"\nReport saved to: {output_file}")
        print(f"Total tasks analyzed: {len(all_tasks)}")
        print(f"Total tokens used: {sum(task.total_tokens for task in all_tasks):,}")
    else:
        print("No LLM calls found in any log files.")


if __name__ == "__main__":
    main()
