"""
E2B Browser Use - Multi-Function Agent Runner

This module provides a flexible main function that can run multiple types of tasks
in an E2B desktop sandbox environment. It supports:

1. Bedrock tasks - Run browser automation using bedrock.py
2. Python scripts - Execute custom Python scripts
3. Shell commands - Run arbitrary shell commands
4. Package installation - Install packages using pip, npm, or apt
5. Browser automation - Run custom browser automation scripts
6. File operations - Copy files to the sandbox
7. File downloads - Download files from sandbox to local machine
8. Browser session persistence - Save and restore browser sessions across sandbox instances

Browser Session Persistence:
    The module automatically saves browser session data (cookies, local storage, profiles)
    from ~/.config/browseruse/profiles/default in the sandbox to /home/<USER>/10x-sales-agent/cookie locally.
    On the next sandbox startup, this data is automatically restored to maintain session
    continuity across different sandbox instances.

Usage Examples:
    # Single task (backward compatibility)
    main(query="Open google.com")

    # Multiple tasks with browser session persistence
    tasks = [
        {'type': 'bedrock', 'query': 'Open google.com'},
        {'type': 'shell', 'command': 'ls -la'},
        {'type': 'install', 'package': 'requests', 'manager': 'pip'},
        {'type': 'download_files', 'download_dir': '~/Downloads', 'local_download_dir': './downloads'},
        {'type': 'save_browser_session', 'local_session_dir': '/home/<USER>/10x-sales-agent/cookie'},
        {'type': 'restore_browser_session', 'local_session_dir': '/home/<USER>/10x-sales-agent/cookie'}
    ]
    main(tasks=tasks)

    # Disable automatic browser session restoration
    main(query="Open google.com", restore_browser_session=False)
"""
import webbrowser
import os
import sys
import logging
import hashlib
import json
import csv
import glob
import time
import shutil
from dotenv import load_dotenv
from e2b_desktop import Sandbox
import boto3
from botocore.exceptions import ClientError
from path_manager import get_path_manager, get_current_downloads_path, get_current_processed_path

# Try to import pandas, fall back to basic CSV handling if not available
try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False
    logger = logging.getLogger(__name__)
    logger.warning("pandas not available, using basic CSV conversion methods")

# Load environment variables from .env file
load_dotenv()

# Configure logger
logger = logging.getLogger(__name__)

def open_desktop_stream(open_browser=False):
    # Create a new desktop sandbox
    logger.info("Creating new desktop sandbox...")
    try:
        # Log environment variables (redacting sensitive values)
        api_key = os.environ.get("API_KEY")
        template = os.environ.get("TEMPLATE")
        domain = os.environ.get("DOMAIN")
        timeout = int(os.environ.get("TIMEOUT", 1200))
        
        logger.info(f"Using template: {template}, domain: {domain}, timeout: {timeout}")
        logger.info(f"API_KEY present: {bool(api_key)}")
        
        # Create sandbox with detailed error handling
        logger.info("Initializing Sandbox object...")
        desktop = Sandbox(
                api_key=api_key,
                template=template,
                domain=domain,
                timeout=timeout,
                metadata={
                    "purpose": "sandbox-on-aws"
                },
                resolution=[1920, 1080]
            )
        logger.info(f"Sandbox object initialized, sandbox_id: {desktop.sandbox_id}")
        
        # Check if sandbox has required attributes
        logger.info(f"Sandbox status: {getattr(desktop, 'status', 'unknown')}")
        logger.info(f"Sandbox ready: {getattr(desktop, 'ready', False)}")
    except Exception as e:
        logger.error(f"Error creating sandbox: {e}", exc_info=True)
        raise

    # Stream the application's window
    # Note: There can be only one stream at a time
    # You need to stop the current stream before streaming another application
    try:
        logger.info("Starting stream...")
        desktop.stream.start(
            require_auth=True
        )
        logger.info("Stream started successfully")

        # Get the stream auth key
        logger.info("Getting stream auth key...")
        auth_key = desktop.stream.get_auth_key()
        logger.info("Auth key retrieved successfully")

        # Get and log the stream URL
        logger.info("Getting stream URL...")
        stream_url = desktop.stream.get_url(auth_key=auth_key)
        logger.info(f'Stream URL generated: {stream_url}')
    except Exception as e:
        logger.error(f"Error setting up stream: {e}", exc_info=True)
        raise
    
    # Open in browser if requested
    if open_browser:
        try:
            # Try the default Chrome browser
            webbrowser.get("chrome").open(stream_url)
        except webbrowser.Error:
            # Fallback for macOS: use 'open -a "Google Chrome"'
            if sys.platform == "darwin":
                os.system(f'open -a "Google Chrome" "{stream_url}"')
            else:
                # Fallback to default browser
                webbrowser.open(stream_url)
    
    return desktop

def create_sts():
    try:
        logger.info("Creating STS session...")
        # Ensure environment variables are loaded
        load_dotenv()
        access_key_id = os.getenv("AWS_ACCESS_KEY_ID")
        secret_access_key = os.getenv("AWS_SECRET_ACCESS_KEY")
        logger.info(f"AWS_ACCESS_KEY_ID present: {bool(access_key_id)}")
        logger.info(f"AWS_SECRET_ACCESS_KEY present: {bool(secret_access_key)}")

        if not access_key_id or not secret_access_key:
            error_msg = "AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY must be set in .env file"
            logger.error(error_msg)
            raise Exception(error_msg)
        
        sts_client = boto3.client('sts',
            aws_access_key_id=access_key_id,
            aws_secret_access_key=secret_access_key
        )
        response = sts_client.get_caller_identity()
        account_id = response['Account']
        
        role_arn = f"arn:aws:iam::{account_id}:role/Bedrock-Role"
        role_session_name = "e2b-desktop"
        
        response = sts_client.assume_role(
            RoleArn=role_arn,
            RoleSessionName=role_session_name,
            DurationSeconds=900
        )
        
        credentials = response['Credentials']
        
        logger.info(f"Successfully created STS session, valid until {credentials['Expiration']}")
        return credentials

    except ClientError as e:
        error_msg = f"Failed to create STS session: {e}"
        logger.error(error_msg)
        return None

def setup_environment(desktop, restore_browser_session=True):
    logger.info("Setting up sandbox environment...")

    # Restore browser session data if available and requested
    if restore_browser_session:
        logger.info("Attempting to restore browser session data...")
        try:
            session_restored = upload_browser_session_data(desktop)
            if session_restored:
                logger.info("Browser session data restored successfully")
            else:
                logger.info("No previous browser session data found or restoration failed")
        except Exception as e:
            logger.error(f"Error restoring browser session data: {e}")

    # Copy files to sandbox
    try:
        logger.info("Copying files to sandbox...")
        with open('bedrock.py', 'r') as f1:
            _code = f1.read()
            desktop.files.write('/tmp/bedrock.py', _code)
            logger.info("Copied bedrock.py to /tmp/bedrock.py")
    except Exception as e:
        logger.error(f"Error copying files: {e}")

    credentials = create_sts()
    creds_content = f"""[default]
    aws_access_key_id={credentials['AccessKeyId']}
    aws_secret_access_key={credentials['SecretAccessKey']}
    aws_session_token={credentials['SessionToken']}
    """
    desktop.files.write('~/.aws/credentials', creds_content)

    # Install Playwright browser
    logger.info("Installing Playwright browser...")
    #desktop.commands.run('playwright install chromium --with-deps --no-shell')
    desktop.commands.run('playwright install chromium')

    logger.info("Installing zip...")
    desktop.commands.run('sudo apt-get install -y zip')

    logger.info("Environment setup completed successfully")


def run_bedrock_task(desktop, query):
    """Run a single bedrock.py task with periodic file downloads"""
    logger.info(f"[BEDROCK_TASK] Starting bedrock.py with query: {query[:100]}...")

    # Check if DEBUG mode is enabled
    debug_mode = os.getenv("DEBUG", "false").lower() in ("true", "1", "yes")
    if debug_mode:
        logger.info("[BEDROCK_TASK] DEBUG mode is enabled - task will not stop automatically")

    # Track all downloaded files during execution
    all_downloaded_files = []

    # Track agent history
    agent_history = None

    # Write query to a temporary file to avoid command line escaping issues
    query_file_path = "/tmp/bedrock_query.txt"
    try:
        desktop.files.write(query_file_path, query)
        logger.info(f"[BEDROCK_TASK] Query written to {query_file_path}")

        # Write the bedrock script
        desktop.files.write('./tmp/bedrock.py', "./bedrock.py")
        logger.info("[BEDROCK_TASK] Created modified bedrock script that can read from file")

        # Preserve temporary files by creating a backup directory
        backup_dir = "/tmp/bedrock_backup"
        desktop.commands.run(f"mkdir -p {backup_dir}")
        logger.info(f"[BEDROCK_TASK] Created backup directory: {backup_dir}")

        # Initial download of any existing files before starting
        logger.info("[BEDROCK_TASK] Performing initial file download before task execution...")
        initial_files = download_temp_files_from_multiple_dirs(desktop)
        all_downloaded_files.extend(initial_files)

        # Start bedrock task in background with threading to allow periodic downloads
        import threading
        import time

        task_completed = threading.Event()
        task_result = {"result": None, "error": None}

        def run_bedrock_script():
            try:
                logger.info("[BEDROCK_TASK] Starting bedrock script execution...")

                # Create a modified bedrock script that saves history
                modified_bedrock_content = f'''
import argparse
import asyncio
import os
import sys
import json

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

import boto3  # type: ignore
from botocore.config import Config
from langchain_aws import ChatBedrockConverse  # type: ignore
from browser_use import Agent, Controller
from browser_use.browser import BrowserProfile
from browser_use.browser import BrowserSession

def get_llm():
    config = Config(retries={{'max_attempts': 10, 'mode': 'adaptive'}})
    bedrock_client = boto3.client('bedrock-runtime', region_name='us-west-2', config=config)

    return ChatBedrockConverse(
        model_id="us.anthropic.claude-3-5-sonnet-20241022-v2:0",
        temperature=0.0,
        max_tokens=None,
        client=bedrock_client,
    )

extend_system_message = """
Always follow the below rule.
1. Think step by step before you take any action.
2. When performing a search task, prioritize opening https://www.bing.com for searching.
3. If an Ad is shown, you should click the "Skip/Close/Cancel" button to close it.
4. The final output should answer the user's question in English.
"""

# Read query from file
with open('{query_file_path}', 'r', encoding='utf-8') as f:
    task = f.read().strip()

llm = get_llm()

browser_session = BrowserSession(
    browser_profile=BrowserProfile(
        downloads_path='~/Downloads',
        user_data_dir='~/.config/browseruse/profiles/default',
    )
)

controller = Controller()

initial_actions = [
    {{'go_to_url': {{'url': 'https://seerfar.com/admin/product-search.html', 'new_tab': True}}}},
]

agent = Agent(
    task=task,
    llm=llm,
    browser_session=browser_session,
    use_vision=True,
    message_context=extend_system_message,
    controller=controller,
    validate_output=True,
    initial_actions=initial_actions,
)

async def main():
    history = await agent.run(max_steps=30)

    # Save history to a temporary file for retrieval
    history_data = history.model_dump() if hasattr(history, 'model_dump') else dict(history)
    with open('/tmp/agent_history.json', 'w', encoding='utf-8') as f:
        json.dump(history_data, f, indent=2, ensure_ascii=False)

    return history

if __name__ == "__main__":
    asyncio.run(main())
'''

                # Write the modified bedrock script
                desktop.files.write('/tmp/bedrock_with_history.py', modified_bedrock_content)

                result = desktop.commands.run(
                    f"python3.11 /tmp/bedrock_with_history.py",
                    on_stdout=lambda data: logger.info(f"[BEDROCK] {{data}}"),
                    on_stderr=lambda data: logger.error(f"[BEDROCK] {{data}}"),
                    timeout=1200
                )
                task_result["result"] = result
                logger.info(f"[BEDROCK_TASK] Script execution completed with result: {{result}}")

                # Try to read the agent history file
                try:
                    history_content = desktop.files.read('/tmp/agent_history.json')
                    if history_content:
                        task_result["agent_history"] = json.loads(history_content)
                        logger.info("[BEDROCK_TASK] Successfully captured agent history")
                    else:
                        logger.warning("[BEDROCK_TASK] Agent history file is empty")
                except Exception as history_error:
                    logger.error(f"[BEDROCK_TASK] Failed to read agent history: {{history_error}}")
                    task_result["agent_history"] = None

            except Exception as e:
                task_result["error"] = e
                logger.error(f"[BEDROCK_TASK] Script execution failed: {{e}}")
            finally:
                task_completed.set()

        # Start the bedrock script in a separate thread
        script_thread = threading.Thread(target=run_bedrock_script)
        script_thread.start()

        # Periodic file downloads while script is running
        download_interval = int(os.getenv("DOWNLOAD_INTERVAL", 30))  # Download interval from env or default 30 seconds
        logger.info(f"[BEDROCK_TASK] Using download interval: {download_interval} seconds")
        last_download_time = time.time()

        while not task_completed.is_set():
            current_time = time.time()

            # Check if it's time for periodic download
            if current_time - last_download_time >= download_interval:
                logger.info("[BEDROCK_TASK] Performing periodic file download during execution...")
                try:
                    periodic_files = download_temp_files_from_multiple_dirs(desktop)
                    all_downloaded_files.extend(periodic_files)
                    last_download_time = current_time
                    logger.info(f"[BEDROCK_TASK] Periodic download completed. Total files so far: {len(all_downloaded_files)}")
                except Exception as e:
                    logger.error(f"[BEDROCK_TASK] Error during periodic download: {e}")

            # Wait a bit before checking again
            time.sleep(5)

        # Wait for the script thread to complete
        script_thread.join(timeout=10)

        # Final download after task completion
        logger.info("[BEDROCK_TASK] Performing final file download after task completion...")
        final_files = download_temp_files_from_multiple_dirs(desktop)
        all_downloaded_files.extend(final_files)

        # Check for any errors
        if task_result["error"]:
            raise task_result["error"]

        result = task_result["result"]
        agent_history = task_result.get("agent_history")
        logger.info(f"[BEDROCK_TASK] Task completed with result: {result}")
        logger.info(f"[BEDROCK_TASK] Total files downloaded during execution: {len(all_downloaded_files)}")

        if agent_history:
            logger.info("[BEDROCK_TASK] Agent history captured successfully")
        else:
            logger.warning("[BEDROCK_TASK] No agent history captured")

        # If DEBUG mode is enabled, keep the sandbox running for manual testing
        if debug_mode:
            logger.info("[BEDROCK_TASK] DEBUG mode: Keeping sandbox running for manual testing...")
            logger.info("[BEDROCK_TASK] You can now login to the sandbox and test manually.")
            logger.info("[BEDROCK_TASK] The sandbox will remain active until manually terminated.")

            # Keep the function running indefinitely in debug mode
            try:
                while True:
                    time.sleep(60)  # Sleep for 1 minute intervals
                    logger.info("[BEDROCK_TASK] DEBUG mode: Sandbox still running... (sleeping for 60 seconds)")

                    # Continue periodic downloads even in debug mode
                    try:
                        debug_files = download_temp_files_from_multiple_dirs(desktop)
                        all_downloaded_files.extend(debug_files)
                        if debug_files:
                            logger.info(f"[BEDROCK_TASK] DEBUG mode: Downloaded {len(debug_files)} additional files")
                    except Exception as e:
                        logger.error(f"[BEDROCK_TASK] DEBUG mode: Error during debug download: {e}")

            except KeyboardInterrupt:
                logger.info("[BEDROCK_TASK] DEBUG mode: Received keyboard interrupt, allowing function to continue...")

        # Return both result and agent history
        return {
            "result": result,
            "agent_history": agent_history,
            "downloaded_files": all_downloaded_files
        }
    except Exception as e:
        logger.error(f"[BEDROCK_TASK] Error in run_bedrock_task: {e}")
        raise

def run_python_script(desktop, script_path, args=""):
    """Run a Python script in the sandbox"""
    logger.info(f"Running Python script: {script_path} with args: {args}")
    result = desktop.commands.run(
        f"python3.11 {script_path} {args}",
        on_stdout=lambda data: logger.info(f"[SCRIPT] {data}"),
        on_stderr=lambda data: logger.error(f"[SCRIPT] {data}"),
        timeout=1200
    )
    logger.info(f"Script completed with result: {result}")
    return result

def run_shell_command(desktop, command):
    """Run a shell command in the sandbox"""
    logger.info(f"Running shell command: {command}")
    result = desktop.commands.run(
        command,
        on_stdout=lambda data: logger.info(f"[SHELL] {data}"),
        on_stderr=lambda data: logger.error(f"[SHELL] {data}"),
        timeout=1200
    )
    logger.info(f"Command completed with result: {result}")
    return result

def install_package(desktop, package_name, package_manager="pip"):
    """Install a package in the sandbox"""
    logger.info(f"Installing package: {package_name} using {package_manager}")
    if package_manager == "pip":
        command = f"pip install {package_name}"
    elif package_manager == "npm":
        command = f"npm install {package_name}"
    elif package_manager == "apt":
        command = f"apt-get update && apt-get install -y {package_name}"
    else:
        logger.error(f"Unsupported package manager: {package_manager}")
        return None

    return run_shell_command(desktop, command)

def run_browser_automation(desktop, script_content, script_name="automation.py"):
    """Run browser automation script in the sandbox"""
    logger.info(f"Running browser automation script: {script_name}")

    # Write the script to the sandbox
    script_path = f"/tmp/{script_name}"
    desktop.files.write(script_path, script_content)
    logger.info(f"Script written to {script_path}")

    # Run the script
    result = run_python_script(desktop, script_path)
    return result

def copy_file_to_sandbox(desktop, local_path, remote_path):
    """Copy a local file to the sandbox"""
    logger.info(f"Copying file from {local_path} to {remote_path}")
    try:
        with open(local_path, 'r') as f:
            content = f.read()
        desktop.files.write(remote_path, content)
        logger.info(f"File copied successfully to {remote_path}")
        return True
    except Exception as e:
        logger.error(f"Error copying file: {e}")
        return False

def download_browser_session_data(desktop, local_session_dir="/home/<USER>/10x-sales-agent/cookie"):
    """
    Download browser session data from sandbox to local directory for persistence.

    Args:
        desktop: E2B desktop sandbox instance
        local_session_dir: Local directory to save browser session data

    Returns:
        bool: True if successful, False otherwise
    """
    logger.info(f"[BROWSER_SESSION] Starting download of browser session data to {local_session_dir}")

    # Browser profile directory in sandbox
    sandbox_profile_dir = "~/.config/browseruse/profiles/default"

    try:
        # Create local session directory if it doesn't exist
        os.makedirs(local_session_dir, exist_ok=True)
        logger.info(f"[BROWSER_SESSION] Created/verified local session directory: {local_session_dir}")

        # Check if browser profile directory exists in sandbox
        check_result = desktop.commands.run(f"test -d {sandbox_profile_dir} && echo 'exists' || echo 'not_exists'")
        if "not_exists" in check_result.stdout:
            logger.warning(f"[BROWSER_SESSION] Browser profile directory {sandbox_profile_dir} does not exist in sandbox")
            return False

        # Get list of all files and directories in the browser profile recursively
        logger.info(f"[BROWSER_SESSION] Scanning browser profile directory: {sandbox_profile_dir}")
        find_result = desktop.commands.run(f"find {sandbox_profile_dir} -type f")

        if not find_result.stdout.strip():
            logger.warning(f"[BROWSER_SESSION] No files found in browser profile directory")
            return False

        # Parse file paths
        file_paths = [f.strip() for f in find_result.stdout.split('\n') if f.strip()]
        logger.info(f"[BROWSER_SESSION] Found {len(file_paths)} files to download")

        downloaded_count = 0
        failed_count = 0

        for file_path in file_paths:
            try:
                # Calculate relative path from profile directory
                relative_path = file_path.replace(f"{sandbox_profile_dir}/", "").replace(f"{sandbox_profile_dir}", "")
                if relative_path.startswith("/"):
                    relative_path = relative_path[1:]

                # Create local file path
                local_file_path = os.path.join(local_session_dir, relative_path)

                # Create local directory structure if needed
                local_dir = os.path.dirname(local_file_path)
                if local_dir:
                    os.makedirs(local_dir, exist_ok=True)

                # Read file content from sandbox
                try:
                    file_content = desktop.files.read(file_path)

                    # Write to local file (handle both text and binary content)
                    if isinstance(file_content, str):
                        with open(local_file_path, 'w', encoding='utf-8') as f:
                            f.write(file_content)
                    else:
                        with open(local_file_path, 'wb') as f:
                            f.write(file_content)

                    downloaded_count += 1
                    logger.debug(f"[BROWSER_SESSION] Downloaded: {relative_path}")

                except Exception as read_error:
                    logger.error(f"[BROWSER_SESSION] Error reading file {file_path}: {read_error}")
                    failed_count += 1
                    continue

            except Exception as e:
                logger.error(f"[BROWSER_SESSION] Error processing file {file_path}: {e}")
                failed_count += 1
                continue

        logger.info(f"[BROWSER_SESSION] Browser session download completed:")
        logger.info(f"[BROWSER_SESSION] - Successfully downloaded: {downloaded_count} files")
        logger.info(f"[BROWSER_SESSION] - Failed downloads: {failed_count} files")
        logger.info(f"[BROWSER_SESSION] - Session data saved to: {local_session_dir}")

        return downloaded_count > 0

    except Exception as e:
        logger.error(f"[BROWSER_SESSION] Error downloading browser session data: {e}")
        return False

def upload_browser_session_data(desktop, local_session_dir="/home/<USER>/10x-sales-agent/cookie"):
    """
    Upload browser session data from local directory to sandbox for persistence.

    Args:
        desktop: E2B desktop sandbox instance
        local_session_dir: Local directory containing browser session data

    Returns:
        bool: True if successful, False otherwise
    """
    logger.info(f"[BROWSER_SESSION] Starting upload of browser session data from {local_session_dir}")

    # Browser profile directory in sandbox
    sandbox_profile_dir = "~/.config/browseruse/profiles/default"

    try:
        # Check if local session directory exists
        if not os.path.exists(local_session_dir):
            logger.warning(f"[BROWSER_SESSION] Local session directory {local_session_dir} does not exist")
            return False

        # Create browser profile directory in sandbox
        logger.info(f"[BROWSER_SESSION] Creating browser profile directory in sandbox: {sandbox_profile_dir}")
        desktop.commands.run(f"mkdir -p {sandbox_profile_dir}")

        # Get list of all files in local session directory recursively
        logger.info(f"[BROWSER_SESSION] Scanning local session directory: {local_session_dir}")
        local_files = []
        for root, dirs, files in os.walk(local_session_dir):
            for file in files:
                local_file_path = os.path.join(root, file)
                relative_path = os.path.relpath(local_file_path, local_session_dir)
                local_files.append((local_file_path, relative_path))

        if not local_files:
            logger.warning(f"[BROWSER_SESSION] No files found in local session directory")
            return False

        logger.info(f"[BROWSER_SESSION] Found {len(local_files)} files to upload")

        uploaded_count = 0
        failed_count = 0

        for local_file_path, relative_path in local_files:
            try:
                # Calculate sandbox file path
                sandbox_file_path = f"{sandbox_profile_dir}/{relative_path}"

                # Create directory structure in sandbox if needed
                sandbox_dir = os.path.dirname(sandbox_file_path)
                if sandbox_dir != sandbox_profile_dir:
                    desktop.commands.run(f"mkdir -p {sandbox_dir}")

                # Read local file content
                try:
                    # Try reading as text first
                    try:
                        with open(local_file_path, 'r', encoding='utf-8') as f:
                            file_content = f.read()
                    except UnicodeDecodeError:
                        # If text reading fails, read as binary and encode as base64
                        with open(local_file_path, 'rb') as f:
                            binary_content = f.read()
                        import base64
                        file_content = base64.b64encode(binary_content).decode('utf-8')
                        # For binary files, we'll need to decode them in the sandbox
                        # Create a script to handle binary file writing
                        decode_script = f"""
import base64
with open('{sandbox_file_path}', 'wb') as f:
    f.write(base64.b64decode('{file_content}'))
"""
                        desktop.files.write('/tmp/decode_binary.py', decode_script)
                        desktop.commands.run('python3.11 /tmp/decode_binary.py')
                        uploaded_count += 1
                        logger.debug(f"[BROWSER_SESSION] Uploaded (binary): {relative_path}")
                        continue

                    # Write text file to sandbox
                    desktop.files.write(sandbox_file_path, file_content)
                    uploaded_count += 1
                    logger.debug(f"[BROWSER_SESSION] Uploaded: {relative_path}")

                except Exception as read_error:
                    logger.error(f"[BROWSER_SESSION] Error reading local file {local_file_path}: {read_error}")
                    failed_count += 1
                    continue

            except Exception as e:
                logger.error(f"[BROWSER_SESSION] Error processing file {local_file_path}: {e}")
                failed_count += 1
                continue

        logger.info(f"[BROWSER_SESSION] Browser session upload completed:")
        logger.info(f"[BROWSER_SESSION] - Successfully uploaded: {uploaded_count} files")
        logger.info(f"[BROWSER_SESSION] - Failed uploads: {failed_count} files")
        logger.info(f"[BROWSER_SESSION] - Session data restored to: {sandbox_profile_dir}")

        return uploaded_count > 0

    except Exception as e:
        logger.error(f"[BROWSER_SESSION] Error uploading browser session data: {e}")
        return False

def download_files_from_sandbox(desktop, download_dir="/home/<USER>/Downloads", local_download_dir=None, preserve_temp_files=False):
    """Download all files from the sandbox Downloads directory to local machine"""
    # Use current session downloads path if not specified
    if local_download_dir is None:
        try:
            local_download_dir = get_current_downloads_path()
        except RuntimeError:
            # Fallback to legacy path if no session is active
            local_download_dir = "/home/<USER>/10x-sales-agent/downloads"

    logger.info(f"[FILE_DOWNLOAD] Starting download from sandbox {download_dir} to local {local_download_dir}")

    # Track downloaded files for cleanup management
    downloaded_files = []

    try:
        # Create local download directory if it doesn't exist
        import os
        os.makedirs(local_download_dir, exist_ok=True)
        logger.info(f"[FILE_DOWNLOAD] Created/verified local download directory: {local_download_dir}")

        # List files in the sandbox download directory
        try:
            # Try to list files in the Downloads directory
            result = desktop.commands.run(f"ls -la {download_dir}")
            logger.info(f"[FILE_DOWNLOAD] Files in sandbox {download_dir}: {result}")

            # Get list of all files using ls command
            file_list_result = desktop.commands.run(f"ls -1 {download_dir}")
            logger.info(f"[FILE_DOWNLOAD] File list result: {file_list_result}")

            if not file_list_result.stdout.strip():
                logger.warning(f"[FILE_DOWNLOAD] No files found in Downloads directory {download_dir}")
                return []

            # Parse the file names and create full paths
            file_names = [f.strip() for f in file_list_result.stdout.split('\n') if f.strip()]
            all_files = [f"{download_dir}/{filename}" for filename in file_names]
            logger.info(f"[FILE_DOWNLOAD] Found {len(all_files)} files to download: {all_files}")

            for file_path in all_files:
                try:
                    # Extract filename from path
                    filename = file_path.split('/')[-1]

                    # Create timestamped filename to avoid overwrites
                    import time
                    timestamp = int(time.time())
                    name, ext = os.path.splitext(filename)
                    timestamped_filename = f"{name}_{timestamp}{ext}"
                    local_file_path = os.path.join(local_download_dir, timestamped_filename)

                    logger.info(f"[FILE_DOWNLOAD] Downloading {file_path} to {local_file_path}")

                    # Read file content from sandbox and write to local file
                    try:
                        file_content = desktop.files.read(file_path)
                        logger.info(f"[FILE_DOWNLOAD] Successfully read file content, length: {len(file_content) if file_content else 0}")

                        # Write to local file
                        with open(local_file_path, 'w', encoding='utf-8') as f:
                            f.write(file_content)

                        logger.info(f"[FILE_DOWNLOAD] Successfully downloaded {filename} to {local_file_path}")
                        downloaded_files.append(local_file_path)

                        # If not preserving temp files, mark for cleanup (but don't delete yet)
                        if not preserve_temp_files:
                            logger.info(f"[FILE_DOWNLOAD] File {file_path} marked for potential cleanup after download verification")

                    except Exception as read_error:
                        logger.error(f"[FILE_DOWNLOAD] Error reading file {file_path}: {read_error}")
                        continue  # Continue with other files instead of raising

                except Exception as e:
                    logger.error(f"[FILE_DOWNLOAD] Error downloading file {file_path}: {e}")
                    continue

            logger.info(f"[FILE_DOWNLOAD] Successfully downloaded {len(downloaded_files)} files: {downloaded_files}")
            return downloaded_files

        except Exception as e:
            logger.error(f"[FILE_DOWNLOAD] Error listing files in {download_dir}: {e}")
            return []

    except Exception as e:
        logger.error(f"[FILE_DOWNLOAD] Error in download_files_from_sandbox: {e}")
        return []

def download_temp_files_from_multiple_dirs(desktop, temp_dirs=None, local_download_dir=None):
    """Download files from multiple temporary directories in the sandbox"""
    if temp_dirs is None:
        temp_dirs = ["/home/<USER>/Downloads", "~/Downloads"]

    # Use current session downloads path if not specified
    if local_download_dir is None:
        try:
            local_download_dir = get_current_downloads_path()
        except RuntimeError:
            # Fallback to legacy path if no session is active
            local_download_dir = "/home/<USER>/10x-sales-agent/downloads"

    logger.info(f"[TEMP_FILE_DOWNLOAD] Scanning multiple directories for temporary files: {temp_dirs}")
    all_downloaded_files = []

    for temp_dir in temp_dirs:
        try:
            # Expand tilde if present
            if temp_dir.startswith("~/"):
                temp_dir = temp_dir.replace("~", "/home/<USER>")

            logger.info(f"[TEMP_FILE_DOWNLOAD] Checking directory: {temp_dir}")

            # Check if directory exists
            check_result = desktop.commands.run(f"test -d {temp_dir} && echo 'exists' || echo 'not_exists'")
            if "not_exists" in check_result.stdout:
                logger.info(f"[TEMP_FILE_DOWNLOAD] Directory {temp_dir} does not exist, skipping")
                continue

            # Download files from this directory
            downloaded_files = download_files_from_sandbox(
                desktop,
                download_dir=temp_dir,
                local_download_dir=local_download_dir,
                preserve_temp_files=True
            )
            all_downloaded_files.extend(downloaded_files)

        except Exception as e:
            logger.error(f"[TEMP_FILE_DOWNLOAD] Error downloading from {temp_dir}: {e}")
            continue

    logger.info(f"[TEMP_FILE_DOWNLOAD] Total files downloaded from all temp directories: {len(all_downloaded_files)}")
    return all_downloaded_files

def move_csv_files_to_processed(downloads_dir=None):
    """Move all CSV files from downloads directory to processed subdirectory"""
    # Use current session downloads path if not specified
    if downloads_dir is None:
        try:
            downloads_dir = get_current_downloads_path()
        except RuntimeError:
            # Fallback to legacy path if no session is active
            downloads_dir = "/home/<USER>/10x-sales-agent/downloads"

    logger.info(f"[FILE_CLEANUP] Moving CSV files to processed directory: {downloads_dir}")

    # Use path manager to get processed directory
    try:
        processed_dir = get_current_processed_path()
    except RuntimeError:
        # Fallback to legacy processed path
        processed_dir = os.path.join(downloads_dir, "processed")
        os.makedirs(processed_dir, exist_ok=True)

    # Find all CSV files in downloads directory (excluding processed subdirectory)
    csv_files = []
    for file in glob.glob(os.path.join(downloads_dir, "*.csv")):
        if 'processed' not in file and os.path.isfile(file):
            csv_files.append(file)

    logger.info(f"[FILE_CLEANUP] Found {len(csv_files)} CSV files to move to processed directory")

    moved_files = []
    for file_path in csv_files:
        try:
            filename = os.path.basename(file_path)
            processed_file_path = os.path.join(processed_dir, filename)

            # Check if file already exists in processed directory
            if os.path.exists(processed_file_path):
                # Add timestamp to avoid conflicts
                timestamp = int(time.time())
                name, ext = os.path.splitext(filename)
                new_filename = f"{name}_{timestamp}{ext}"
                processed_file_path = os.path.join(processed_dir, new_filename)
                logger.info(f"[FILE_CLEANUP] File exists, using new name: {new_filename}")

            # Move the file
            shutil.move(file_path, processed_file_path)
            moved_files.append(processed_file_path)
            logger.info(f"[FILE_CLEANUP] Moved {filename} to processed/{os.path.basename(processed_file_path)}")

        except Exception as e:
            logger.error(f"[FILE_CLEANUP] Error moving {os.path.basename(file_path)}: {e}")

    logger.info(f"[FILE_CLEANUP] Successfully moved {len(moved_files)} CSV files to processed directory")
    return moved_files

def get_file_hash(file_path):
    """Calculate SHA256 hash of file content for duplicate detection"""
    try:
        with open(file_path, 'rb') as f:
            file_hash = hashlib.sha256()
            chunk = f.read(8192)
            while chunk:
                file_hash.update(chunk)
                chunk = f.read(8192)
        return file_hash.hexdigest()
    except Exception as e:
        logger.error(f"[FILE_PROCESSING] Error calculating hash for {file_path}: {e}")
        return None

def convert_to_csv(file_path):
    """Convert various file formats to CSV"""
    logger.info(f"[FILE_PROCESSING] Attempting to convert {file_path} to CSV format")

    base_name = os.path.splitext(file_path)[0]
    csv_path = f"{base_name}.csv"

    try:
        # Check file extension to determine conversion method
        file_ext = os.path.splitext(file_path)[1].lower()

        if file_ext == '.xlsx':
            # Convert Excel file
            if not PANDAS_AVAILABLE:
                logger.warning(f"[FILE_PROCESSING] pandas not available, cannot convert Excel file: {file_path}")
                return file_path
            logger.info(f"[FILE_PROCESSING] Converting Excel file: {file_path}")
            df = pd.read_excel(file_path)
            df.to_csv(csv_path, index=False)
            logger.info(f"[FILE_PROCESSING] Successfully converted Excel to CSV: {csv_path}")
            return csv_path

        elif file_ext == '.json':
            # Convert JSON file
            logger.info(f"[FILE_PROCESSING] Converting JSON file: {file_path}")
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # Handle different JSON structures
            if isinstance(data, list) and len(data) > 0 and isinstance(data[0], dict):
                # List of dictionaries - convert to CSV
                if PANDAS_AVAILABLE:
                    df = pd.DataFrame(data)
                    df.to_csv(csv_path, index=False)
                else:
                    # Manual CSV conversion without pandas
                    with open(csv_path, 'w', newline='', encoding='utf-8') as csvfile:
                        if data:
                            fieldnames = data[0].keys()
                            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                            writer.writeheader()
                            writer.writerows(data)
                logger.info(f"[FILE_PROCESSING] Successfully converted JSON to CSV: {csv_path}")
                return csv_path
            else:
                logger.warning(f"[FILE_PROCESSING] JSON file {file_path} is not in tabular format, keeping original")
                return file_path

        elif file_ext == '.tsv':
            # Convert TSV file
            logger.info(f"[FILE_PROCESSING] Converting TSV file: {file_path}")
            if PANDAS_AVAILABLE:
                df = pd.read_csv(file_path, sep='\t')
                df.to_csv(csv_path, index=False)
            else:
                # Manual TSV to CSV conversion
                with open(file_path, 'r', encoding='utf-8') as tsvfile:
                    with open(csv_path, 'w', newline='', encoding='utf-8') as csvfile:
                        reader = csv.reader(tsvfile, delimiter='\t')
                        writer = csv.writer(csvfile)
                        for row in reader:
                            writer.writerow(row)
            logger.info(f"[FILE_PROCESSING] Successfully converted TSV to CSV: {csv_path}")
            return csv_path

        elif file_ext == '' or file_ext == '.txt':
            # Try to detect delimited data in text files
            logger.info(f"[FILE_PROCESSING] Analyzing text file for delimited data: {file_path}")

            with open(file_path, 'r', encoding='utf-8') as f:
                sample = f.read(1024)  # Read first 1KB to detect format

            # Try different delimiters
            delimiters = [',', '\t', ';', '|']
            best_delimiter = None
            max_columns = 0

            for delimiter in delimiters:
                try:
                    lines = sample.split('\n')[:5]  # Check first 5 lines
                    column_counts = [len(line.split(delimiter)) for line in lines if line.strip()]
                    if column_counts and max(column_counts) > max_columns and max(column_counts) > 1:
                        max_columns = max(column_counts)
                        best_delimiter = delimiter
                except:
                    continue

            if best_delimiter and max_columns > 1:
                logger.info(f"[FILE_PROCESSING] Detected delimiter '{best_delimiter}' with {max_columns} columns")
                if PANDAS_AVAILABLE:
                    df = pd.read_csv(file_path, sep=best_delimiter)
                    df.to_csv(csv_path, index=False)
                else:
                    # Manual conversion without pandas
                    with open(file_path, 'r', encoding='utf-8') as infile:
                        with open(csv_path, 'w', newline='', encoding='utf-8') as csvfile:
                            reader = csv.reader(infile, delimiter=best_delimiter)
                            writer = csv.writer(csvfile)
                            for row in reader:
                                writer.writerow(row)
                logger.info(f"[FILE_PROCESSING] Successfully converted delimited text to CSV: {csv_path}")
                return csv_path
            else:
                logger.warning(f"[FILE_PROCESSING] Could not detect tabular data in {file_path}, keeping original")
                return file_path
        else:
            logger.info(f"[FILE_PROCESSING] File {file_path} has unsupported extension {file_ext}, keeping original")
            return file_path

    except Exception as e:
        logger.error(f"[FILE_PROCESSING] Error converting {file_path} to CSV: {e}")
        return file_path

def process_downloaded_files(downloaded_files):
    """
    Post-process downloaded files: convert to CSV and remove duplicates

    Args:
        downloaded_files (list): List of downloaded file paths

    Returns:
        list: List of processed unique CSV file paths
    """
    logger.info(f"[FILE_PROCESSING] Starting post-processing of {len(downloaded_files)} downloaded files")

    if not downloaded_files:
        logger.info("[FILE_PROCESSING] No files to process")
        return []

    processed_files = []
    file_hashes = {}  # hash -> file_path mapping for duplicate detection

    # Step 1: Convert files to CSV format
    logger.info("[FILE_PROCESSING] Step 1: Converting files to CSV format")
    for file_path in downloaded_files:
        try:
            if not os.path.exists(file_path):
                logger.warning(f"[FILE_PROCESSING] File not found: {file_path}")
                continue

            logger.info(f"[FILE_PROCESSING] Processing file: {file_path}")

            # Check if file already has .csv extension
            if file_path.lower().endswith('.csv'):
                logger.info(f"[FILE_PROCESSING] File already has .csv extension: {file_path}")

                # Check if it's a UUID-based filename and rename it to something more descriptive
                filename = os.path.basename(file_path)
                if len(filename) > 30 and '-' in filename and filename.count('-') >= 4:
                    # This looks like a UUID-based filename, rename it
                    timestamp = time.strftime("%Y%m%d_%H%M%S")
                    new_filename = f"Seerfar-Product-Export-{timestamp}.csv"
                    new_path = os.path.join(os.path.dirname(file_path), new_filename)

                    try:
                        os.rename(file_path, new_path)
                        logger.info(f"[FILE_PROCESSING] Renamed UUID-based file to: {new_filename}")
                        processed_files.append(new_path)
                    except Exception as e:
                        logger.warning(f"[FILE_PROCESSING] Could not rename file {file_path}: {e}")
                        processed_files.append(file_path)
                else:
                    processed_files.append(file_path)
            else:
                # Attempt conversion to CSV
                converted_path = convert_to_csv(file_path)
                processed_files.append(converted_path)

                # If conversion created a new file, remove the original (unless it's the same file)
                if converted_path != file_path and os.path.exists(converted_path):
                    try:
                        os.remove(file_path)
                        logger.info(f"[FILE_PROCESSING] Removed original file after conversion: {file_path}")
                    except Exception as e:
                        logger.warning(f"[FILE_PROCESSING] Could not remove original file {file_path}: {e}")

        except Exception as e:
            logger.error(f"[FILE_PROCESSING] Error processing file {file_path}: {e}")
            # Keep the original file if processing fails
            processed_files.append(file_path)

    logger.info(f"[FILE_PROCESSING] Conversion complete. {len(processed_files)} files to check for duplicates")

    # Step 2: Remove duplicates based on content hash
    logger.info("[FILE_PROCESSING] Step 2: Detecting and removing duplicate files")
    unique_files = []
    duplicates_removed = []

    for file_path in processed_files:
        try:
            if not os.path.exists(file_path):
                logger.warning(f"[FILE_PROCESSING] File not found during duplicate check: {file_path}")
                continue

            # Calculate file hash
            file_hash = get_file_hash(file_path)
            if file_hash is None:
                logger.warning(f"[FILE_PROCESSING] Could not calculate hash for {file_path}, keeping file")
                unique_files.append(file_path)
                continue

            # Check if we've seen this content before
            if file_hash in file_hashes:
                # Duplicate found
                existing_file = file_hashes[file_hash]
                logger.info(f"[FILE_PROCESSING] Duplicate detected: {file_path} is identical to {existing_file}")

                # Choose which file to keep (prefer more descriptive filename or first encountered)
                existing_name = os.path.basename(existing_file)
                current_name = os.path.basename(file_path)

                if len(current_name) > len(existing_name) or 'download' not in current_name.lower():
                    # Current file has better name, replace the existing one
                    logger.info(f"[FILE_PROCESSING] Keeping {file_path} over {existing_file} (better filename)")
                    try:
                        os.remove(existing_file)
                        unique_files.remove(existing_file)
                        duplicates_removed.append(existing_file)
                    except (ValueError, OSError) as e:
                        logger.warning(f"[FILE_PROCESSING] Could not remove duplicate {existing_file}: {e}")

                    file_hashes[file_hash] = file_path
                    unique_files.append(file_path)
                else:
                    # Keep existing file, remove current duplicate
                    logger.info(f"[FILE_PROCESSING] Removing duplicate {file_path}, keeping {existing_file}")
                    try:
                        os.remove(file_path)
                        duplicates_removed.append(file_path)
                    except OSError as e:
                        logger.warning(f"[FILE_PROCESSING] Could not remove duplicate {file_path}: {e}")
                        # If we can't remove it, keep it to be safe
                        unique_files.append(file_path)
            else:
                # New unique file
                file_hashes[file_hash] = file_path
                unique_files.append(file_path)
                logger.info(f"[FILE_PROCESSING] Unique file added: {file_path}")

        except Exception as e:
            logger.error(f"[FILE_PROCESSING] Error during duplicate detection for {file_path}: {e}")
            # Keep the file if duplicate detection fails
            unique_files.append(file_path)

    # Log summary
    logger.info(f"[FILE_PROCESSING] Post-processing complete:")
    logger.info(f"[FILE_PROCESSING] - Original files: {len(downloaded_files)}")
    logger.info(f"[FILE_PROCESSING] - After conversion: {len(processed_files)}")
    logger.info(f"[FILE_PROCESSING] - Final unique files: {len(unique_files)}")
    logger.info(f"[FILE_PROCESSING] - Duplicates removed: {len(duplicates_removed)}")

    if duplicates_removed:
        logger.info(f"[FILE_PROCESSING] Removed duplicate files: {duplicates_removed}")

    if unique_files:
        logger.info(f"[FILE_PROCESSING] Final processed files: {unique_files}")

    return unique_files

def main(tasks=None, query=None, process_id=None, restore_browser_session=True):
    """
    Main function that can run multiple functions/tasks

    Args:
        tasks (list): List of task dictionaries with 'type' and parameters
                     Example: [
                         {'type': 'bedrock', 'query': 'Open google.com'},
                         {'type': 'script', 'path': '/tmp/script.py', 'args': '--param value'},
                         {'type': 'shell', 'command': 'ls -la /tmp'},
                         {'type': 'save_browser_session', 'local_session_dir': '/home/<USER>/10x-sales-agent/cookie'},
                         {'type': 'restore_browser_session', 'local_session_dir': '/home/<USER>/10x-sales-agent/cookie'}
                     ]
        query (str): Single query for backward compatibility (runs bedrock task)
        process_id (str): Unique process identifier for organizing files
        restore_browser_session (bool): Whether to restore browser session data on startup (default: True)
    """

    logger.info("Starting main workflow")

    # Initialize session paths if process_id is provided
    if process_id:
        logger.info(f"Initializing session paths for process ID: {process_id}")
        path_manager = get_path_manager()
        session_folder = path_manager.create_session_folder(process_id)
        logger.info(f"Created session folder: {session_folder}")

    desktop = open_desktop_stream()
    setup_environment(desktop, restore_browser_session)

    results = []

    try:
        # Backward compatibility: if only query is provided, run single bedrock task
        if query and not tasks:
            logger.info(f"Running single bedrock task with query: {query}")
            result = run_bedrock_task(desktop, query)
            results.append(result)

        # Run multiple tasks if provided
        elif tasks:
            logger.info(f"Running {len(tasks)} tasks")
            for i, task in enumerate(tasks):
                logger.info(f"Executing task {i+1}/{len(tasks)}: {task}")

                task_type = task.get('type', 'bedrock')

                if task_type == 'bedrock':
                    query = task.get('query', '')
                    result = run_bedrock_task(desktop, query)
                    results.append(result)

                elif task_type == 'script':
                    script_path = task.get('path', '')
                    args = task.get('args', '')
                    result = run_python_script(desktop, script_path, args)
                    results.append(result)

                elif task_type == 'shell':
                    command = task.get('command', '')
                    result = run_shell_command(desktop, command)
                    results.append(result)

                elif task_type == 'install':
                    package_name = task.get('package', '')
                    package_manager = task.get('manager', 'pip')
                    result = install_package(desktop, package_name, package_manager)
                    results.append(result)

                elif task_type == 'browser_automation':
                    script_content = task.get('script_content', '')
                    script_name = task.get('script_name', 'automation.py')
                    result = run_browser_automation(desktop, script_content, script_name)
                    results.append(result)

                elif task_type == 'copy_file':
                    local_path = task.get('local_path', '')
                    remote_path = task.get('remote_path', '')
                    result = copy_file_to_sandbox(desktop, local_path, remote_path)
                    results.append(result)

                elif task_type == 'download_files':
                    download_dir = task.get('download_dir', '~/Downloads')
                    local_download_dir = task.get('local_download_dir', './downloads')
                    result = download_files_from_sandbox(desktop, download_dir, local_download_dir)
                    results.append(result)

                elif task_type == 'save_browser_session':
                    local_session_dir = task.get('local_session_dir', '/home/<USER>/10x-sales-agent/cookie')
                    result = download_browser_session_data(desktop, local_session_dir)
                    results.append(result)

                elif task_type == 'restore_browser_session':
                    local_session_dir = task.get('local_session_dir', '/home/<USER>/10x-sales-agent/cookie')
                    result = upload_browser_session_data(desktop, local_session_dir)
                    results.append(result)

                else:
                    logger.warning(f"Unknown task type: {task_type}")
                    results.append(None)

        else:
            logger.warning("No tasks or query provided")

        # Download files from sandbox after all tasks are completed
        logger.info("[MAIN_DOWNLOAD] Starting comprehensive file download after all tasks completed...")
        try:
            # Download from multiple directories to ensure we capture all temporary files
            logger.info("[MAIN_DOWNLOAD] Downloading from all temporary directories...")
            all_final_files = download_temp_files_from_multiple_dirs(desktop)

            # Also download from the standard Downloads directory
            logger.info("[MAIN_DOWNLOAD] Downloading from standard Downloads directory...")
            standard_downloads = download_files_from_sandbox(desktop, preserve_temp_files=True)
            all_final_files.extend(standard_downloads)

            # Remove basic duplicates based on filename
            unique_files = list(set(all_final_files))

            # Check for any existing files in the downloads directory (from periodic downloads)
            import glob
            try:
                local_download_dir = get_current_downloads_path()
            except RuntimeError:
                # Fallback to legacy path if no session is active
                local_download_dir = "/home/<USER>/10x-sales-agent/downloads"
            existing_files = []
            if os.path.exists(local_download_dir):
                # Get all files in downloads directory, excluding the processed subdirectory
                pattern = os.path.join(local_download_dir, "*")
                all_items = glob.glob(pattern)
                existing_files = [f for f in all_items if os.path.isfile(f) and not f.endswith('.csv') and 'processed' not in f]
                logger.info(f"[MAIN_DOWNLOAD] Found {len(existing_files)} existing files in downloads directory")

            # Combine newly downloaded files with existing files
            all_files_to_process = list(set(unique_files + existing_files))

            if all_files_to_process:
                logger.info(f"[MAIN_DOWNLOAD] Total files to process: {len(all_files_to_process)}")
                logger.info(f"[MAIN_DOWNLOAD] Files to process: {all_files_to_process}")

                # Post-process downloaded files: convert to CSV and remove content duplicates
                logger.info("[MAIN_DOWNLOAD] Starting post-processing of all downloaded files...")
                processed_files = process_downloaded_files(all_files_to_process)

                if processed_files:
                    logger.info(f"[MAIN_DOWNLOAD] Post-processing complete. Final files: {len(processed_files)}")
                    results.append({"downloaded_files": all_files_to_process, "processed_files": processed_files})
                else:
                    logger.warning("[MAIN_DOWNLOAD] No files remained after post-processing")
                    results.append({"downloaded_files": all_files_to_process, "processed_files": []})
            else:
                logger.info("[MAIN_DOWNLOAD] No files found to process")

        except Exception as e:
            logger.error(f"[MAIN_DOWNLOAD] Error downloading files from sandbox: {e}")

        # Final cleanup logging
        logger.info("[MAIN_DOWNLOAD] File download and processing completed")

        # Move all processed CSV files to processed directory
        logger.info("[MAIN_DOWNLOAD] Moving CSV files to processed directory...")
        try:
            moved_files = move_csv_files_to_processed()
            if moved_files:
                logger.info(f"[MAIN_DOWNLOAD] Moved {len(moved_files)} CSV files to processed directory")
            else:
                logger.info("[MAIN_DOWNLOAD] No CSV files to move to processed directory")
        except Exception as e:
            logger.error(f"[MAIN_DOWNLOAD] Error moving CSV files to processed directory: {e}")

    except Exception as e:
        logger.error(f"Error during task execution: {e}", exc_info=True)
        raise
    finally:
        # Save browser session data before cleanup
        logger.info("Saving browser session data for persistence...")
        try:
            session_saved = download_browser_session_data(desktop)
            if session_saved:
                logger.info("Browser session data saved successfully for next sandbox instance")
            else:
                logger.warning("Failed to save browser session data")
        except Exception as e:
            logger.error(f"Error saving browser session data: {e}")

        # Check if DEBUG mode is enabled before killing sandbox
        debug_mode = os.getenv("DEBUG", "false").lower() in ("true", "1", "yes")
        if debug_mode:
            logger.info("DEBUG mode: Skipping sandbox cleanup to allow manual testing...")
            logger.info("Note: Sandbox will remain active and may incur costs. Remember to manually terminate when done.")
        else:
            logger.info("Killing desktop sandbox...")
            desktop.kill()
            logger.info("Desktop sandbox killed successfully")

    logger.info(f"All tasks completed. Results: {results}")
    return results

if __name__ == "__main__":
    # Example 1: Single task (backward compatibility)
    # main(query="Open google.com")

    # Example 2: Multiple tasks with different types including browser session management
    example_tasks = [
        # Install required packages
        {'type': 'install', 'package': 'requests beautifulsoup4', 'manager': 'pip'},

        # Copy a custom script to sandbox
        # {'type': 'copy_file', 'local_path': './custom_script.py', 'remote_path': '/tmp/custom_script.py'},

        # Restore browser session from previous run (optional - done automatically by default)
        # {'type': 'restore_browser_session', 'local_session_dir': '/home/<USER>/cookie'},

        # Run bedrock tasks
        {'type': 'bedrock', 'query': 'Open google.com'},
        {'type': 'bedrock', 'query': 'Search for Python tutorials'},

        # Run shell commands
        {'type': 'shell', 'command': 'ls -la /tmp'},
        {'type': 'shell', 'command': 'ps aux | grep python'},

        # Run custom scripts
        {'type': 'script', 'path': '/tmp/bedrock.py', 'args': '--query "Check weather"'},

        # Run browser automation script
        {
            'type': 'browser_automation',
            'script_name': 'web_scraper.py',
            'script_content': '''
import requests
from bs4 import BeautifulSoup

def scrape_example():
    print("Running web scraping example...")
    # Add your web scraping logic here
    print("Web scraping completed!")

if __name__ == "__main__":
    scrape_example()
'''
        },

        # Save browser session data manually (optional - done automatically at end)
        # {'type': 'save_browser_session', 'local_session_dir': '/home/<USER>/10x-sales-agent/cookie'},

        # Download files from sandbox (optional - files are auto-downloaded after all tasks)
        {'type': 'download_files', 'download_dir': '~/Downloads', 'local_download_dir': './downloads'}
    ]

    # Run single task for testing
    main(query="Open google.com")

    # Uncomment to run multiple tasks
    # main(tasks=example_tasks)
